{"configurations": [{"name": "DSP_BASICMATH", "includePath": ["d:\\桌面\\f4锁相pfc\\CORE", "d:\\桌面\\f4锁相pfc\\SYSTEM\\delay", "d:\\桌面\\f4锁相pfc\\SYSTEM\\sys", "d:\\桌面\\f4锁相pfc\\SYSTEM\\usart", "d:\\桌面\\f4锁相pfc\\USER", "d:\\桌面\\f4锁相pfc\\HARDWARE\\LED", "d:\\桌面\\f4锁相pfc\\HARDWARE\\LCD", "d:\\桌面\\f4锁相pfc\\FWLIB\\inc", "d:\\桌面\\f4锁相pfc\\HARDWARE\\KEY", "d:\\桌面\\f4锁相pfc\\HARDWARE\\TIMER", "d:\\桌面\\f4锁相pfc\\DSP_LIB", "d:\\桌面\\f4锁相pfc\\DSP_LIB\\Include", "d:\\桌面\\f4锁相pfc\\HARDWARE\\svpwm", "d:\\桌面\\f4锁相pfc\\HARDWARE\\vofa", "d:\\桌面\\f4锁相pfc\\HARDWARE\\OLED", "d:\\桌面\\f4锁相pfc\\HARDWARE\\MENU", "d:\\桌面\\f4锁相pfc\\HARDWARE\\SPWM", "d:\\桌面\\f4锁相pfc\\HARDWARE\\PFC", "D:\\DPJ\\keil 5\\ARM\\ARMCC\\include", "D:\\DPJ\\keil 5\\ARM\\ARMCC\\include\\rw", "d:\\桌面\\f4锁相pfc\\HARDWARE\\ADC", "d:\\桌面\\f4锁相pfc\\FWLIB\\src", "d:\\桌面\\f4锁相pfc", "${default}", "${workspaceFolder}/**"], "defines": ["STM32F40_41xxx", "USE_STDPERIPH_DRIVER", "ARM_MATH_CM4", "__CC_ARM", "ARM_MATH_MATRIX_CHECK", "ARM_MATH_ROUNDING", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}, {"name": "PFC", "includePath": ["d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\CORE", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\SYSTEM\\delay", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\SYSTEM\\sys", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\SYSTEM\\usart", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\USER", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\LED", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\FWLIB\\inc", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\KEY", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\TIMER", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\DSP_LIB", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\DSP_LIB\\Include", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\ADC", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\PFC", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\SPWM", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\OLED", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\MENU", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\vofa", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\PID", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\HARDWARE\\DAC", "D:\\DPJ\\keil 5\\ARM\\ARMCC\\include", "D:\\DPJ\\keil 5\\ARM\\ARMCC\\include\\rw", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环\\FWLIB\\src", "d:\\桌面\\2023电赛\\2023单相逆变2 主调电流环", "${default}", "${workspaceFolder}/**"], "defines": ["STM32F40_41xxx", "USE_STDPERIPH_DRIVER", "ARM_MATH_CM4", "__CC_ARM", "ARM_MATH_MATRIX_CHECK", "ARM_MATH_ROUNDING", "__TARGET_FPU_VFP", "__FPU_PRESENT", "=1", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}