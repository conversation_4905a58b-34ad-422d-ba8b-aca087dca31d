Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to oled.o(.text) for OLED_Init
    main.o(.text) refers to spwm.o(.text) for TIM1_SPWM_Init
    main.o(.text) refers to vofa.o(.text) for Vofa_Init
    main.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_CtrlPWMOutputs
    main.o(.text) refers to main.o(.bss) for vofa1
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    vofa.o(.text) refers to stm32f4xx_usart.o(.text) for USART_SendData
    vofa.o(.text) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    vofa.o(.text) refers to vofa.o(.constdata) for justFloatTail
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to key.o(.data) for key_up
    oled.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    oled.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    oled.o(.text) refers to delay.o(.text) for delay_us
    oled.o(.text) refers to cdcmple.o(.text) for __aeabi_cdcmple
    oled.o(.text) refers to dfixi.o(.text) for __aeabi_d2iz
    oled.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    oled.o(.text) refers to dadd.o(.text) for __aeabi_dsub
    oled.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    oled.o(.text) refers to oled.o(.constdata) for OLED_F8x16
    spwm.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    spwm.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    spwm.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    spwm.o(.text) refers to misc.o(.text) for NVIC_Init
    spwm.o(.text) refers to spwm.o(.data) for Frequency
    spwm.o(.text) refers to spwm.o(.constdata) for Sin_wave_Table
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to spwm.o(.text) for TIM1_UP_TIM10_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(.text) for fputc
    printfa.o(i.__0printf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0vprintf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing vofa.o(.rev16_text), (4 bytes).
    Removing vofa.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing spwm.o(.rev16_text), (4 bytes).
    Removing spwm.o(.revsh_text), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing startup_stm32f40_41xxx.o(HEAP), (512 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (1124 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (936 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing dneg.o(.text), (6 bytes).

54 unused section(s) (total 5226 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\OLED\OLED.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\SPWM\spwm.c                  0x00000000   Number         0  spwm.o ABSOLUTE
    ..\HARDWARE\vofa\Vofa.c                  0x00000000   Number         0  vofa.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\OLED\\OLED.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HARDWARE\\SPWM\\spwm.c               0x00000000   Number         0  spwm.o ABSOLUTE
    ..\\HARDWARE\\vofa\\Vofa.c               0x00000000   Number         0  vofa.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x0800019c   Section        0  main.o(.text)
    .text                                    0x08000220   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x0800023c   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x0800023d   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x0800044c   Section        0  vofa.o(.text)
    Vofa_GetByte                             0x08000555   Thumb Code    84  vofa.o(.text)
    .text                                    0x08000664   Section        0  led.o(.text)
    .text                                    0x080006a4   Section        0  key.o(.text)
    .text                                    0x080007d8   Section        0  oled.o(.text)
    .text                                    0x08000e00   Section        0  spwm.o(.text)
    .text                                    0x08001080   Section        0  delay.o(.text)
    .text                                    0x08001184   Section        0  usart.o(.text)
    .text                                    0x080012cc   Section       36  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x080012cc   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x080012f0   Section        0  misc.o(.text)
    .text                                    0x080013d0   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08001664   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08001cc0   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08002114   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x080028b3   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08002915   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08002977   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x080029e3   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08002db6   Section        0  dadd.o(.text)
    .text                                    0x08002f04   Section        0  dmul.o(.text)
    .text                                    0x08002fe8   Section        0  dflti.o(.text)
    .text                                    0x0800300a   Section        0  dfixi.o(.text)
    .text                                    0x08003048   Section       48  cdcmple.o(.text)
    .text                                    0x08003078   Section        0  uidiv.o(.text)
    .text                                    0x080030a4   Section        0  uldiv.o(.text)
    .text                                    0x08003106   Section        0  llshl.o(.text)
    .text                                    0x08003124   Section        0  llushr.o(.text)
    .text                                    0x08003144   Section        0  llsshr.o(.text)
    .text                                    0x08003168   Section        0  depilogue.o(.text)
    .text                                    0x08003168   Section        0  iusefp.o(.text)
    .text                                    0x08003222   Section        0  ddiv.o(.text)
    .text                                    0x08003300   Section        0  dfixul.o(.text)
    .text                                    0x08003330   Section       48  cdrcmple.o(.text)
    .text                                    0x08003360   Section       36  init.o(.text)
    i.__0vsnprintf                           0x08003384   Section        0  printfa.o(i.__0vsnprintf)
    i.__scatterload_copy                     0x080033b0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080033be   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080033c0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080033d0   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080033d1   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08003554   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08003555   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08003c30   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08003c31   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08003c54   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08003c55   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08003c82   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08003c83   Thumb Code    22  printfa.o(i._snputc)
    .constdata                               0x08003c98   Section        6  vofa.o(.constdata)
    cmdTail                                  0x08003c98   Data           2  vofa.o(.constdata)
    justFloatTail                            0x08003c9a   Data           4  vofa.o(.constdata)
    .constdata                               0x08003c9e   Section     1520  oled.o(.constdata)
    .constdata                               0x0800428e   Section      400  spwm.o(.constdata)
    .data                                    0x20000000   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section        1  key.o(.data)
    key_up                                   0x20000014   Data           1  key.o(.data)
    .data                                    0x20000018   Section       20  spwm.o(.data)
    .data                                    0x2000002c   Section        4  delay.o(.data)
    fac_us                                   0x2000002c   Data           1  delay.o(.data)
    fac_ms                                   0x2000002e   Data           2  delay.o(.data)
    .data                                    0x20000030   Section        6  usart.o(.data)
    .data                                    0x20000036   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000036   Data          16  stm32f4xx_rcc.o(.data)
    .bss                                     0x20000048   Section      216  main.o(.bss)
    .bss                                     0x20000120   Section      200  usart.o(.bss)
    STACK                                    0x200001e8   Section     1024  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    main                                     0x0800019d   Thumb Code   114  main.o(.text)
    NMI_Handler                              0x08000221   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x08000223   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x08000227   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x0800022b   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800022f   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x08000233   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000235   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x08000237   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000239   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000319   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08000371   Thumb Code   174  system_stm32f4xx.o(.text)
    Vofa_Init                                0x0800044d   Thumb Code    18  vofa.o(.text)
    Vofa_SendDataCallBack                    0x0800045f   Thumb Code    48  vofa.o(.text)
    Vofa_SendData                            0x0800048f   Thumb Code    20  vofa.o(.text)
    Vofa_JustFloat                           0x080004a3   Thumb Code    36  vofa.o(.text)
    Vofa_Printf                              0x080004c7   Thumb Code    44  vofa.o(.text)
    Vofa_GetDataCallBack                     0x080004f3   Thumb Code    14  vofa.o(.text)
    Vofa_ReceiveData                         0x08000501   Thumb Code    84  vofa.o(.text)
    Vofa_ReadCmd                             0x080005a9   Thumb Code    76  vofa.o(.text)
    Vofa_ReadLine                            0x080005f5   Thumb Code    52  vofa.o(.text)
    Vofa_ReadData                            0x08000629   Thumb Code    48  vofa.o(.text)
    LED_Init                                 0x08000665   Thumb Code    60  led.o(.text)
    KEY_Init                                 0x080006a5   Thumb Code    42  key.o(.text)
    KEY_Scan                                 0x080006cf   Thumb Code   258  key.o(.text)
    OLED_I2C_Init                            0x080007d9   Thumb Code    86  oled.o(.text)
    OLED_I2C_Start                           0x0800082f   Thumb Code    56  oled.o(.text)
    OLED_I2C_Stop                            0x08000867   Thumb Code    52  oled.o(.text)
    OLED_I2C_SendByte                        0x0800089b   Thumb Code    98  oled.o(.text)
    OLED_WriteCommand                        0x080008fd   Thumb Code    32  oled.o(.text)
    OLED_WriteData                           0x0800091d   Thumb Code    32  oled.o(.text)
    OLED_SetCursor                           0x0800093d   Thumb Code    34  oled.o(.text)
    OLED_Clear                               0x0800095f   Thumb Code    42  oled.o(.text)
    OLED_ShowChar                            0x08000989   Thumb Code   102  oled.o(.text)
    OLED_ShowString                          0x080009ef   Thumb Code    40  oled.o(.text)
    OLED_Pow                                 0x08000a17   Thumb Code    20  oled.o(.text)
    OLED_ShowNum                             0x08000a2b   Thumb Code    68  oled.o(.text)
    OLED_ShowSignedNum                       0x08000a6f   Thumb Code   102  oled.o(.text)
    OLED_ShowHexNum                          0x08000ad5   Thumb Code    84  oled.o(.text)
    OLED_Float                               0x08000b29   Thumb Code   506  oled.o(.text)
    OLED_ShowBinNum                          0x08000d23   Thumb Code    62  oled.o(.text)
    OLED_Init                                0x08000d61   Thumb Code   158  oled.o(.text)
    TIM1_SPWM_Init                           0x08000e01   Thumb Code   316  spwm.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000f3d   Thumb Code   286  spwm.o(.text)
    delay_init                               0x08001081   Thumb Code    52  delay.o(.text)
    delay_us                                 0x080010b5   Thumb Code    72  delay.o(.text)
    delay_xms                                0x080010fd   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08001145   Thumb Code    56  delay.o(.text)
    _sys_exit                                0x08001185   Thumb Code     4  usart.o(.text)
    fputc                                    0x08001189   Thumb Code    22  usart.o(.text)
    uart_init                                0x0800119f   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x08001243   Thumb Code   122  usart.o(.text)
    Reset_Handler                            0x080012cd   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080012e7   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x080012f1   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x080012fb   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x08001365   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08001373   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08001395   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x080013d1   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x080014dd   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x0800156d   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800157f   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080015a1   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x080015b3   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080015bb   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x080015cd   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x080015d5   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x080015d9   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x080015dd   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x080015e7   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x080015eb   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x080015f3   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08001665   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x080016b7   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x080016c5   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08001701   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08001739   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x0800174d   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x08001753   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08001781   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08001787   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x080017a7   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x080017ad   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x080017bb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x080017c1   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x080017d5   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x080017db   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x080017e1   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x080017fd   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08001819   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x0800182d   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08001839   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x0800184d   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08001861   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08001877   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08001955   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800198b   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08001993   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800199b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x080019a1   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x080019bb   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x080019d7   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x080019eb   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x080019ff   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x08001a13   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x08001a19   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x08001a3b   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08001a89   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08001aab   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08001acd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08001aef   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08001b11   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08001b33   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08001b55   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08001b77   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08001b99   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08001bbb   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08001bdd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08001bff   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08001c21   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08001c43   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08001c6b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08001c8d   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08001c9f   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08001cb5   Thumb Code     8  stm32f4xx_rcc.o(.text)
    USART_DeInit                             0x08001cc1   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x08001d8f   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08001e5b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08001e73   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08001e93   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08001e9f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08001eb7   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001ec7   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001edd   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08001ef5   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08001efd   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08001f07   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08001f19   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08001f31   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001f43   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08001f55   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08001f6d   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001f77   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08001f8f   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08001f9f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001fb7   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08001fcf   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08001fe1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08001ff9   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x0800200b   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08002055   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x0800206f   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08002081   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x080020f7   Thumb Code    30  stm32f4xx_usart.o(.text)
    TIM_DeInit                               0x08002115   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x0800226f   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x080022d7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x080022e9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x080022ef   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x08002301   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x08002305   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x08002309   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x0800230f   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08002315   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x0800232d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08002345   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x0800235d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x0800236f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x08002381   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08002399   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x0800240b   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x080024a5   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08002571   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x080025e1   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x080025f5   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x0800264b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x0800264f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x08002653   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x08002657   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x0800265b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x0800266d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x08002687   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08002699   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x080026b3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x080026c5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080026df   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x080026f1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x0800270b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x0800271d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08002737   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08002749   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x08002763   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08002775   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x0800278d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x0800279f   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x080027b7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x080027c9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080027db   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x080027f5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x0800280f   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08002829   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x08002843   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x0800285d   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x0800287b   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08002899   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08002903   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x0800295d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x080029d1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x08002a1d   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x08002a8b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x08002a9d   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08002b19   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x08002b1f   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08002b25   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08002b2b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08002b31   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x08002b51   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08002b63   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x08002b81   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x08002b99   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08002bb1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08002bc3   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x08002bc7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08002bd9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x08002bdf   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08002c01   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08002c07   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x08002c11   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08002c23   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x08002c3b   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08002c47   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08002c59   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08002c71   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x08002caf   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08002ccb   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x08002d01   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08002d21   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x08002d33   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08002d45   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08002d57   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08002d99   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08002db1   Thumb Code     6  stm32f4xx_tim.o(.text)
    __aeabi_dadd                             0x08002db7   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08002ef9   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08002eff   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08002f05   Thumb Code   228  dmul.o(.text)
    __aeabi_i2d                              0x08002fe9   Thumb Code    34  dflti.o(.text)
    __aeabi_d2iz                             0x0800300b   Thumb Code    62  dfixi.o(.text)
    __aeabi_cdcmpeq                          0x08003049   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08003049   Thumb Code    48  cdcmple.o(.text)
    __aeabi_uidiv                            0x08003079   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08003079   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x080030a5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08003107   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08003107   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08003125   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08003125   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08003145   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08003145   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08003169   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08003169   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08003187   Thumb Code   156  depilogue.o(.text)
    __aeabi_ddiv                             0x08003223   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08003301   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08003331   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08003361   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08003361   Thumb Code     0  init.o(.text)
    __0vsnprintf                             0x08003385   Thumb Code    40  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08003385   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08003385   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08003385   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08003385   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __scatterload_copy                       0x080033b1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080033bf   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080033c1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    OLED_F8x16                               0x08003c9e   Data        1520  oled.o(.constdata)
    Sin_wave_Table                           0x0800428e   Data         400  spwm.o(.constdata)
    Region$$Table$$Base                      0x08004420   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08004440   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f4xx.o(.data)
    i                                        0x20000018   Data           4  spwm.o(.data)
    Frequency                                0x2000001c   Data           4  spwm.o(.data)
    sum                                      0x20000020   Data           2  spwm.o(.data)
    Mid_Value                                0x20000024   Data           4  spwm.o(.data)
    A                                        0x20000028   Data           4  spwm.o(.data)
    __stdout                                 0x20000030   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000034   Data           2  usart.o(.data)
    vofa1                                    0x20000048   Data         216  main.o(.bss)
    USART_RX_BUF                             0x20000120   Data         200  usart.o(.bss)
    __initial_sp                             0x200005e8   Data           0  startup_stm32f40_41xxx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00004488, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00004440, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          457    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000000   Code   RO          687  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO          730    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO          733    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO          735    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO          737    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO          738    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO          740    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO          742    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO          731    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000084   Code   RO            3    .text               main.o
    0x08000220   0x08000220   0x0000001a   Code   RO          185    .text               stm32f4xx_it.o
    0x0800023a   0x0800023a   0x00000002   PAD
    0x0800023c   0x0800023c   0x00000210   Code   RO          234    .text               system_stm32f4xx.o
    0x0800044c   0x0800044c   0x00000218   Code   RO          260    .text               vofa.o
    0x08000664   0x08000664   0x00000040   Code   RO          289    .text               led.o
    0x080006a4   0x080006a4   0x00000134   Code   RO          309    .text               key.o
    0x080007d8   0x080007d8   0x00000626   Code   RO          330    .text               oled.o
    0x08000dfe   0x08000dfe   0x00000002   PAD
    0x08000e00   0x08000e00   0x00000280   Code   RO          359    .text               spwm.o
    0x08001080   0x08001080   0x00000104   Code   RO          386    .text               delay.o
    0x08001184   0x08001184   0x00000148   Code   RO          426    .text               usart.o
    0x080012cc   0x080012cc   0x00000024   Code   RO          458    .text               startup_stm32f40_41xxx.o
    0x080012f0   0x080012f0   0x000000e0   Code   RO          464    .text               misc.o
    0x080013d0   0x080013d0   0x00000294   Code   RO          484    .text               stm32f4xx_gpio.o
    0x08001664   0x08001664   0x0000065c   Code   RO          527    .text               stm32f4xx_rcc.o
    0x08001cc0   0x08001cc0   0x00000454   Code   RO          569    .text               stm32f4xx_usart.o
    0x08002114   0x08002114   0x00000ca2   Code   RO          589    .text               stm32f4xx_tim.o
    0x08002db6   0x08002db6   0x0000014e   Code   RO          718    .text               mf_w.l(dadd.o)
    0x08002f04   0x08002f04   0x000000e4   Code   RO          720    .text               mf_w.l(dmul.o)
    0x08002fe8   0x08002fe8   0x00000022   Code   RO          724    .text               mf_w.l(dflti.o)
    0x0800300a   0x0800300a   0x0000003e   Code   RO          726    .text               mf_w.l(dfixi.o)
    0x08003048   0x08003048   0x00000030   Code   RO          728    .text               mf_w.l(cdcmple.o)
    0x08003078   0x08003078   0x0000002c   Code   RO          744    .text               mc_w.l(uidiv.o)
    0x080030a4   0x080030a4   0x00000062   Code   RO          746    .text               mc_w.l(uldiv.o)
    0x08003106   0x08003106   0x0000001e   Code   RO          748    .text               mc_w.l(llshl.o)
    0x08003124   0x08003124   0x00000020   Code   RO          750    .text               mc_w.l(llushr.o)
    0x08003144   0x08003144   0x00000024   Code   RO          752    .text               mc_w.l(llsshr.o)
    0x08003168   0x08003168   0x00000000   Code   RO          754    .text               mc_w.l(iusefp.o)
    0x08003168   0x08003168   0x000000ba   Code   RO          755    .text               mf_w.l(depilogue.o)
    0x08003222   0x08003222   0x000000de   Code   RO          757    .text               mf_w.l(ddiv.o)
    0x08003300   0x08003300   0x00000030   Code   RO          759    .text               mf_w.l(dfixul.o)
    0x08003330   0x08003330   0x00000030   Code   RO          761    .text               mf_w.l(cdrcmple.o)
    0x08003360   0x08003360   0x00000024   Code   RO          763    .text               mc_w.l(init.o)
    0x08003384   0x08003384   0x0000002c   Code   RO          696    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080033b0   0x080033b0   0x0000000e   Code   RO          767    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080033be   0x080033be   0x00000002   Code   RO          768    i.__scatterload_null  mc_w.l(handlers.o)
    0x080033c0   0x080033c0   0x0000000e   Code   RO          769    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080033ce   0x080033ce   0x00000002   PAD
    0x080033d0   0x080033d0   0x00000184   Code   RO          698    i._fp_digits        mc_w.l(printfa.o)
    0x08003554   0x08003554   0x000006dc   Code   RO          699    i._printf_core      mc_w.l(printfa.o)
    0x08003c30   0x08003c30   0x00000024   Code   RO          700    i._printf_post_padding  mc_w.l(printfa.o)
    0x08003c54   0x08003c54   0x0000002e   Code   RO          701    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08003c82   0x08003c82   0x00000016   Code   RO          702    i._snputc           mc_w.l(printfa.o)
    0x08003c98   0x08003c98   0x00000006   Data   RO          261    .constdata          vofa.o
    0x08003c9e   0x08003c9e   0x000005f0   Data   RO          331    .constdata          oled.o
    0x0800428e   0x0800428e   0x00000190   Data   RO          360    .constdata          spwm.o
    0x0800441e   0x0800441e   0x00000002   PAD
    0x08004420   0x08004420   0x00000020   Data   RO          765    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08004488, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08004440, Size: 0x000005e8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08004440   0x00000014   Data   RW          235    .data               system_stm32f4xx.o
    0x20000014   0x08004454   0x00000001   Data   RW          310    .data               key.o
    0x20000015   0x08004455   0x00000003   PAD
    0x20000018   0x08004458   0x00000014   Data   RW          361    .data               spwm.o
    0x2000002c   0x0800446c   0x00000004   Data   RW          387    .data               delay.o
    0x20000030   0x08004470   0x00000006   Data   RW          428    .data               usart.o
    0x20000036   0x08004476   0x00000010   Data   RW          528    .data               stm32f4xx_rcc.o
    0x20000046   0x08004486   0x00000002   PAD
    0x20000048        -       0x000000d8   Zero   RW            4    .bss                main.o
    0x20000120        -       0x000000c8   Zero   RW          427    .bss                usart.o
    0x200001e8        -       0x00000400   Zero   RW          455    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       260          8          0          4          0       1453   delay.o
       308          8          0          1          0        900   key.o
        64          4          0          0          0        511   led.o
       132         18          0          0        216     295239   main.o
       224         20          0          0          0       1789   misc.o
      1574         24       1520          0          0       5807   oled.o
       640         38        400         20          0       1927   spwm.o
        36          8        392          0       1024        816   startup_stm32f40_41xxx.o
       660         44          0          0          0       4129   stm32f4xx_gpio.o
        26          0          0          0          0       1210   stm32f4xx_it.o
      1628         52          0         16          0      12992   stm32f4xx_rcc.o
      3234         60          0          0          0      22988   stm32f4xx_tim.o
      1108         34          0          0          0       7856   stm32f4xx_usart.o
       528         46          0         20          0       1735   system_stm32f4xx.o
       328         16          0          6        200       3282   usart.o
       536         12          6          0          0       4826   vofa.o

    ----------------------------------------------------------------------
     11290        <USER>       <GROUP>         72       1440     367460   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          2          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
      2292         84          0          0          0        516   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3830        <USER>          <GROUP>          0          0       1840   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2618        100          0          0          0        960   mc_w.l
      1210          0          0          0          0        880   mf_w.l

    ----------------------------------------------------------------------
      3830        <USER>          <GROUP>          0          0       1840   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15120        492       2352         72       1440     365716   Grand Totals
     15120        492       2352         72       1440     365716   ELF Image Totals
     15120        492       2352         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                17472 (  17.06kB)
    Total RW  Size (RW Data + ZI Data)              1512 (   1.48kB)
    Total ROM Size (Code + RO Data + RW Data)      17544 (  17.13kB)

==============================================================================

