#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"
#include "key.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "Vofa.h"
#include "oled.h"
#include "menu.h"
#include "spwm.h"

Vofa_HandleTypedef vofa1;

int main(void)

{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    delay_init(168);    
    uart_init(115200); 

    LED_Init();
    KEY_Init();
    OLED_Init();

    TIM1_SPWM_Init(8399, 0); // 20k�ز�Ƶ��
    

    Vofa_Init(&vofa1, VOFA_MODE_SKIP); //
    LED0 = 0;
    LED1 = 1;

//    TIM_CtrlPWMOutputs(TIM1, DISABLE);

    while (1)
    {
			LED0 = !LED0;
      LED1 = !LED1;
			delay_ms(200);
		}
	
}




